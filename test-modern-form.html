<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم الثالث - Modern Form</title>
    <link rel="stylesheet" href="assets/css/rid-cod.css">
    <style>
        body {
            font-family: 'Cairo', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
        }
        .test-info h2 {
            color: #333;
            margin-top: 0;
        }
        .test-info ul {
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h2>اختبار التصميم الثالث (Modern Form)</h2>
            <p>هذا اختبار للتأكد من أن المشاكل التالية تم حلها:</p>
            <ul>
                <li>النص الافتراضي لحقل رقم الهاتف يظهر في الجهة الصحيحة (اليمين)</li>
                <li>التصميم يحافظ على التخطيط الأصلي (حقلين جنباً إلى جنب) حتى على الهاتف</li>
                <li>النص الافتراضي يظهر باللغة العربية بشكل صحيح</li>
                <li>التصميم متجاوب ولكن يحافظ على الهيكل الأصلي</li>
            </ul>
        </div>

        <div class="rid-cod-form-container rid-cod-form-modern">
            <div id="rid-cod-checkout">
                <div class="rid-cod-title">
                    <h3>نموذج الطلب - التصميم الثالث</h3>
                </div>

                <form id="rid-cod-form">
                    <div class="rid-cod-customer-info">
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <span class="rid-input-icon rid-icon-user"></span>
                            <input type="text" id="rid-cod-full-name" name="full_name" placeholder="الاسم الكامل" required>
                        </div>

                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <span class="rid-input-icon rid-icon-phone"></span>
                            <input type="tel" id="rid-cod-phone" name="phone" placeholder="رقم الهاتف" required>
                        </div>

                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <span class="rid-input-icon rid-icon-state"></span>
                            <select id="rid-cod-state" name="state" required>
                                <option value="">اختر الولاية</option>
                                <option value="الجزائر">الجزائر</option>
                                <option value="وهران">وهران</option>
                                <option value="قسنطينة">قسنطينة</option>
                            </select>
                        </div>

                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <span class="rid-input-icon rid-icon-city"></span>
                            <select id="rid-cod-city" name="city" required>
                                <option value="">اختر البلدية</option>
                                <option value="الجزائر العاصمة">الجزائر العاصمة</option>
                                <option value="وهران">وهران</option>
                                <option value="قسنطينة">قسنطينة</option>
                            </select>
                        </div>

                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <span class="rid-input-icon rid-icon-notes"></span>
                            <textarea id="rid-cod-notes" name="notes" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                        </div>
                    </div>

                    <div class="rid-cod-actions-row">
                        <div class="rid-cod-quantity">
                            <div class="rid-cod-quantity-selector">
                                <button type="button" id="rid-cod-decrease">-</button>
                                <input type="number" id="rid-cod-quantity-input" value="1" min="1">
                                <button type="button" id="rid-cod-increase">+</button>
                            </div>
                        </div>

                        <div class="rid-cod-submit">
                            <button type="submit" id="rid-cod-submit-btn">إضافة إلى السلة</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Simple quantity controls
        document.getElementById('rid-cod-decrease').addEventListener('click', function() {
            const input = document.getElementById('rid-cod-quantity-input');
            if (input.value > 1) {
                input.value = parseInt(input.value) - 1;
            }
        });

        document.getElementById('rid-cod-increase').addEventListener('click', function() {
            const input = document.getElementById('rid-cod-quantity-input');
            input.value = parseInt(input.value) + 1;
        });

        // Form submission test
        document.getElementById('rid-cod-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم إرسال النموذج بنجاح! (هذا مجرد اختبار)');
        });
    </script>
</body>
</html>
